{"node-red": {"name": "node-red", "version": "4.0.9-git", "local": false, "user": false, "nodes": {"junction": {"name": "junction", "types": ["junction"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\05-junction.js"}, "inject": {"name": "inject", "types": ["inject"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\20-inject.js"}, "debug": {"name": "debug", "types": ["debug"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\21-debug.js"}, "complete": {"name": "complete", "types": ["complete"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\24-complete.js"}, "catch": {"name": "catch", "types": ["catch"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\25-catch.js"}, "status": {"name": "status", "types": ["status"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\25-status.js"}, "link": {"name": "link", "types": ["link in", "link out", "link call"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\60-link.js"}, "comment": {"name": "comment", "types": ["comment"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\90-comment.js"}, "global-config": {"name": "global-config", "types": ["global-config"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\91-global-config.js"}, "unknown": {"name": "unknown", "types": ["unknown"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\common\\98-unknown.js"}, "function": {"name": "function", "types": ["function"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\10-function.js"}, "switch": {"name": "switch", "types": ["switch"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\10-switch.js"}, "change": {"name": "change", "types": ["change"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\15-change.js"}, "range": {"name": "range", "types": ["range"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\16-range.js"}, "template": {"name": "template", "types": ["template"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\80-template.js"}, "delay": {"name": "delay", "types": ["delay"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\89-delay.js"}, "trigger": {"name": "trigger", "types": ["trigger"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\89-trigger.js"}, "exec": {"name": "exec", "types": ["exec"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\90-exec.js"}, "rbe": {"name": "rbe", "types": ["rbe"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\function\\rbe.js"}, "tls": {"name": "tls", "types": ["tls-config"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\05-tls.js"}, "httpproxy": {"name": "httpproxy", "types": ["http proxy"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\06-httpproxy.js"}, "mqtt": {"name": "mqtt", "types": ["mqtt in", "mqtt out", "mqtt-broker"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\10-mqtt.js"}, "httpin": {"name": "httpin", "types": ["http in", "http response"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\21-httpin.js"}, "httprequest": {"name": "httprequest", "types": ["http request"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\21-httprequest.js"}, "websocket": {"name": "websocket", "types": ["websocket in", "websocket out", "websocket-listener", "websocket-client"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\22-websocket.js"}, "tcpin": {"name": "tcpin", "types": ["tcp in", "tcp out", "tcp request"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\31-tcpin.js"}, "udp": {"name": "udp", "types": ["udp in", "udp out"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\network\\32-udp.js"}, "CSV": {"name": "CSV", "types": ["csv"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\parsers\\70-CSV.js"}, "HTML": {"name": "HTML", "types": ["html"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\parsers\\70-HTML.js"}, "JSON": {"name": "JSON", "types": ["json"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\parsers\\70-JSON.js"}, "XML": {"name": "XML", "types": ["xml"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\parsers\\70-XML.js"}, "YAML": {"name": "YAML", "types": ["yaml"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\parsers\\70-YAML.js"}, "split": {"name": "split", "types": ["split", "join"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\sequence\\17-split.js"}, "sort": {"name": "sort", "types": ["sort"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\sequence\\18-sort.js"}, "batch": {"name": "batch", "types": ["batch"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\sequence\\19-batch.js"}, "file": {"name": "file", "types": ["file", "file in"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\storage\\10-file.js"}, "watch": {"name": "watch", "types": ["watch"], "enabled": true, "local": false, "user": false, "module": "node-red", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\@node-red\\nodes\\core\\storage\\23-watch.js"}}}, "lcp-nodes": {"name": "lcp-nodes", "version": "1.0.43", "local": true, "user": true, "nodes": {"lcp-test": {"name": "lcp-test", "types": ["lcp-test"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-test.js"}, "lcp-wa-handler": {"name": "lcp-wa-handler", "types": ["lcp-wa-handler"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-waHandler.js"}, "lcp-embedding-handler": {"name": "lcp-embedding-handler", "types": ["lcp-embedding-handler"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-embedding.js"}, "lcp-s3-upload": {"name": "lcp-s3-upload", "types": ["s3-upload"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-s3-upload.js"}, "lcp-demo": {"name": "lcp-demo", "types": ["demo"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-demo.js"}, "lcp-ms-teams-bot": {"name": "lcp-ms-teams-bot", "types": ["lcp-ms-teams-bot"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-ms-teams-bot.js"}, "pdf-extract": {"name": "pdf-extract", "types": ["pdf-extract"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-pdf-extract.js"}, "lcp-openai": {"name": "lcp-openai", "types": ["lcp-openai"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-openai.js"}, "file-upload-handler": {"name": "file-upload-handler", "types": ["file-upload-handler"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\file-upload-handler\\file-upload-handler.js"}, "file-format-checker": {"name": "file-format-checker", "types": ["file-format-checker"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\file-format-checker\\file-format-checker.js"}, "pdf-processor": {"name": "pdf-processor", "types": ["pdf-processor"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\pdf-processor\\pdf-processor.js"}, "docx-processor": {"name": "docx-processor", "types": ["docx-processor"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\docx-processor\\docx-processor.js"}, "text-preprocessor": {"name": "text-preprocessor", "types": ["text-preprocessor"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\text-preprocessor\\text-preprocessor.js"}, "openai-integration": {"name": "openai-integration", "types": ["openai-integration"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\openai-integration\\openai-integration.js"}, "combine-results": {"name": "combine-results", "types": ["combine-results"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\combine-results\\combine-results.js"}, "lcp-dynamodb": {"name": "lcp-dynamodb", "types": ["dynamodb"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\lcp-dynamodb.js"}, "receipt-upload-handler": {"name": "receipt-upload-handler", "types": ["receipt-upload-handler"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\receipt-upload-handler\\receipt-upload-handler.js"}, "google-document-ai-processor": {"name": "google-document-ai-processor", "types": ["google-document-ai-processor"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\google-document-ai-processor\\google-document-ai-processor.js"}, "parse-expense-document": {"name": "parse-expense-document", "types": ["parse-expense-document"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\parse-expense-document\\parse-expense-document.js"}, "file-processor": {"name": "file-processor", "types": ["file-processor"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\file-processor\\file-processor.js"}, "payload-chunker": {"name": "payload-chunker", "types": ["payload-chunker"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\payload-chunker\\payload-chunker.js"}, "lcp-agent": {"name": "lcp-agent", "types": ["lcp-agent"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\agent\\lcp-agent.js"}, "Outlook-getMails": {"name": "Outlook-getMails", "types": ["Outlook-getMails"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\Outlook-getMails\\Outlook-getMails.js"}, "Outlook-postMails": {"name": "Outlook-postMails", "types": ["Outlook-postMails"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\Outlook-postMails\\Outlook-postMails.js"}, "github-commit": {"name": "github-commit", "types": ["github-commits"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\github-commit\\github-commit.js"}, "github-commit-details": {"name": "github-commit-details", "types": ["github-commit-details"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\github-commit-details\\github-commit-details.js"}, "configurable-embedding-node": {"name": "configurable-embedding-node", "types": ["configurable-embedding-node"], "enabled": true, "local": true, "user": false, "module": "lcp-nodes", "file": "D:\\Projects\\Web Files\\neartekpod\\ai-rekruit-pro\\recruit-nr-flows\\node_modules\\lcp-nodes\\configurable-embedding-node\\configurable-embedding-node.js"}}}}
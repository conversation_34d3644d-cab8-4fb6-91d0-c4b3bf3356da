{"FileNameMap": {"TableName": "FileNameMap", "KeySchema": [{"AttributeName": "application", "KeyType": "HASH"}, {"AttributeName": "fileuuid", "KeyType": "RANGE"}], "AttributeDefinitions": [{"AttributeName": "application", "AttributeType": "S"}, {"AttributeName": "fileuuid", "AttributeType": "S"}], "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}}, "ResumeStatus": {"TableName": "ResumeStatus", "KeySchema": [{"AttributeName": "application", "KeyType": "HASH"}, {"AttributeName": "fileid", "KeyType": "RANGE"}], "AttributeDefinitions": [{"AttributeName": "application", "AttributeType": "S"}, {"AttributeName": "fileid", "AttributeType": "S"}], "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}}, "ExtractedResume": {"TableName": "ExtractedResume", "KeySchema": [{"AttributeName": "application", "KeyType": "HASH"}, {"AttributeName": "fileid", "KeyType": "RANGE"}], "AttributeDefinitions": [{"AttributeName": "application", "AttributeType": "S"}, {"AttributeName": "fileid", "AttributeType": "S"}], "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}}, "LCPCandidate": {"TableName": "LCPCandidate", "KeySchema": [{"AttributeName": "application", "KeyType": "HASH"}, {"AttributeName": "candidateid", "KeyType": "RANGE"}], "AttributeDefinitions": [{"AttributeName": "application", "AttributeType": "S"}, {"AttributeName": "candidateid", "AttributeType": "S"}, {"AttributeName": "Job_id", "AttributeType": "S"}], "GlobalSecondaryIndexes": [{"IndexName": "Job_id-index", "KeySchema": [{"AttributeName": "Job_id", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}, "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}}], "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}}}
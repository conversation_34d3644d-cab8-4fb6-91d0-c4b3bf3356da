[{"id": "d67b549d72343dd7", "type": "tab", "label": "Flow 1", "disabled": false, "info": "", "env": []}, {"id": "b5b69245969546ac", "type": "function", "z": "d67b549d72343dd7", "name": "function 10", "func": "msg.Job_Id = msg.req.body.Job_Id\nmsg.payload = msg.req.files\nmsg.context_user =  msg.req.headers['context_user']\nmsg.JobDesc = msg.req.body.JobDesc\nreturn msg", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 290, "y": 280, "wires": [["314372771a41c7de"]]}, {"id": "314372771a41c7de", "type": "split", "z": "d67b549d72343dd7", "name": "", "splt": "\\n", "spltType": "str", "arraySplt": 1, "arraySpltType": "len", "stream": false, "addname": "", "property": "payload", "x": 450, "y": 280, "wires": [["07a6bae68f2794d4"]]}, {"id": "03a0034f5b909cf0", "type": "http in", "z": "d67b549d72343dd7", "name": "", "url": "/multi-resume-upload", "method": "post", "upload": true, "swaggerDoc": "", "x": 150, "y": 240, "wires": [["b5b69245969546ac"]]}, {"id": "07a6bae68f2794d4", "type": "function", "z": "d67b549d72343dd7", "name": "function 11", "func": "    const hasFile = msg.payload\n\n    if (hasFile) {\n    const uuidv4 = uuid.v4;\n        msg.originalname = msg.payload.originalname;\n    if (!msg.originalname.endsWith(\".pdf\")) {\n        msg.payload.error = \"file type not supported\";\n        msg.statusCode = 400;\n        return msg;\n    }\n    msg.payload = msg.payload.buffer;\n    msg.filename = uuidv4();\n\n    msg.user = 'contextUser';\n    msg.attachment = msg.payload;\n    msg.fileuuid = msg.filename;\n    return msg\n    } else {\n    msg.payload.error = \"file not found\";\n    msg.statusCode = 400;\n    return msg\n    }\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 590, "y": 220, "wires": [["8620da067576efa1", "7ebf28f1935e0210"]]}, {"id": "7ebf28f1935e0210", "type": "s3-upload", "z": "d67b549d72343dd7", "name": "S3 Upload", "bucket": "RESUME_BUCKET", "operation": "upload", "x": 790, "y": 220, "wires": [["2dac670be7ccc553"], []]}, {"id": "8620da067576efa1", "type": "function", "z": "d67b549d72343dd7", "name": "function 12", "func": "\nmsg.payload = {\n    \"fileuuid\": msg.fileuuid,\n    \"originalname\": msg.originalname\n    };\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 670, "y": 280, "wires": [["bcdfb9483c866a78"]]}, {"id": "bcdfb9483c866a78", "type": "join", "z": "d67b549d72343dd7", "name": "", "mode": "auto", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": false, "accumulate": true, "timeout": "", "count": "", "reduceRight": false, "reduceExp": "", "reduceInit": "", "reduceInitType": "", "reduceFixup": "", "x": 830, "y": 280, "wires": [["db61e791c81a4cbb"]]}, {"id": "db61e791c81a4cbb", "type": "http response", "z": "d67b549d72343dd7", "name": "", "statusCode": "", "headers": {}, "x": 990, "y": 280, "wires": []}, {"id": "2dac670be7ccc553", "type": "function", "z": "d67b549d72343dd7", "name": "function 3", "func": "msg.payload = {\n    \"application\": \"hiring\",\n                \"fileuuid\":msg.filename,\n                \"originalname\":msg.originalname,\n                \"bucket\": \"lcp-resume\",\n                \"status\" : \"File has been uploaded successfully\"\n                }\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 960, "y": 220, "wires": [["c9f9cb4e2f2d0974"]]}, {"id": "c9f9cb4e2f2d0974", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "", "region": "us-east-1", "operation": "create", "tableName": "FileNameMap", "schemaFilePath": "", "x": 1090, "y": 180, "wires": [["5a2ad0dcd0b550af"]]}, {"id": "5a2ad0dcd0b550af", "type": "change", "z": "d67b549d72343dd7", "name": "SetPayload", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "attachment", "tot": "msg"}, {"t": "set", "p": "attachment", "pt": "msg", "to": "null", "tot": "jsonata"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 570, "y": 420, "wires": [["6559ae3dec5e4d1a", "1961cbd2df21a14c"]]}, {"id": "6559ae3dec5e4d1a", "type": "pdf-extract", "z": "d67b549d72343dd7", "name": "", "x": 770, "y": 420, "wires": [["ad74f2b7466f943b", "c74751f826db17c2"]]}, {"id": "c74751f826db17c2", "type": "function", "z": "d67b549d72343dd7", "name": "function 4", "func": "\nmsg.payload = {\n    application:\"hiring\",\n    \"fileid\": msg.filename,\n    \"status\": \"PDF Extracted\",\n    \"timestamp\": new Date().toISOString()\n};\n\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 970, "y": 380, "wires": [["17a685c380439c40"]]}, {"id": "17a685c380439c40", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "", "region": "us-east-1", "operation": "create", "tableName": "ResumeStatus", "schemaFilePath": "", "x": 1170, "y": 380, "wires": [[]]}, {"id": "ad74f2b7466f943b", "type": "text-preprocessor", "z": "d67b549d72343dd7", "name": "", "x": 990, "y": 420, "wires": [["47fb2b6ad819d6d2"]]}, {"id": "47fb2b6ad819d6d2", "type": "function", "z": "d67b549d72343dd7", "name": "Chunks", "func": "msg.payload = msg.payload.chunks\n// if (Array.isArray(msg.payload)) {\n//     msg.payload = msg.payload.chunks;\n// } else {\n//     node.error('Payload is not an array of chunks', msg);\n// }\nreturn msg;\n\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1160, "y": 420, "wires": [["c618a4256a217923"]]}, {"id": "c618a4256a217923", "type": "split", "z": "d67b549d72343dd7", "name": "", "splt": "\\n", "spltType": "str", "arraySplt": 1, "arraySpltType": "len", "stream": false, "addname": "", "property": "payload", "x": 1330, "y": 420, "wires": [["94f34725701f7dcc"]]}, {"id": "94f34725701f7dcc", "type": "function", "z": "d67b549d72343dd7", "name": "Prompt for Resume", "func": "msg.topic = \"GPT-4o-mini\"\nmsg.payload = \"You are an AI assistant specialized in parsing resumes. Extract the requested information and format it as a JSON object and return the JSON object only and nothing else.\" +\n    \"Text of CV from a candidate can be found below\" +\n    \"You need to analyze and extract key information like First Name, Middle Name, Last Name, skills, experience summary and all the other details present in the resume text and return it in JSON object format. return valid JSON object only\" +\n    \"Candidate CV \\n\" + msg.payload\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 790, "y": 480, "wires": [["f4b586ef73b9efa5"]]}, {"id": "f4b586ef73b9efa5", "type": "lcp-openai", "z": "d67b549d72343dd7", "name": "", "model": "gpt-4o-mini", "maxTokens": "2000", "validateJson": false, "x": 960, "y": 480, "wires": [["01dd242390de4c46"]]}, {"id": "01dd242390de4c46", "type": "join", "z": "d67b549d72343dd7", "name": "", "mode": "auto", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": false, "accumulate": "false", "timeout": "", "count": "", "reduceRight": false, "x": 1150, "y": 480, "wires": [["a38faf4f20602a4e"]]}, {"id": "a38faf4f20602a4e", "type": "function", "z": "d67b549d72343dd7", "name": "Prompt for JSON", "func": "msg.topic = \"GPT-4o-mini\";\nmsg.payload = `The following JSON template represents the structure of a resume. Please fill in the missing fields based on the provided text or correct any syntax issues if present. Return only the valid JSON without any additional text also Please send only JSON object and nothing else.\n\nTemplate:\n{\n    \"name\": \"Full Name\",\n    \"email\": \"Email Address\",\n    \"phone_number\": \"Phone Number\",\n    \"key_skills\": [\"Skill 1\", \"Skill 2\", ...],\n    \"years_of_experience\": Number,\n    \"experience_summary\": \"Brief summary\",\n    \"employment_history\": [\n        {\n            \"organization\": \"Company Name\",\n            \"role\": \"Job Title\",\n            \"start_date\": \"YYYY-MM\",\n            \"end_date\": \"YYYY-MM or 'Present'\"\n        },\n        ...\n    ],\n    \"education\": [\n        {\n            \"institution\": \"Institution Name\",\n            \"degree\": \"Degree\",\n            \"field_of_study\": \"Field of Study\",\n            \"start_date\": \"YYYY-MM\",\n            \"end_date\": \"YYYY-MM or 'Present'\"\n        },\n        ...\n    ],\n    \"achievements\": [\"Achievement 1\", \"Achievement 2\", ...],\n    \"certifications\": [\"Certification 1\", \"Certification 2\", ...]\n}\n\nText to process:\n${msg.payload}`;\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 790, "y": 540, "wires": [["8563dabccf5bc394"]]}, {"id": "8563dabccf5bc394", "type": "lcp-openai", "z": "d67b549d72343dd7", "name": "", "model": "gpt-4o-mini", "maxTokens": "2000", "x": 960, "y": 540, "wires": [["fdd8316fef63a4b9", "c4dfb6b98c5c04ee", "0b4d310b8f6c33b6"]]}, {"id": "fdd8316fef63a4b9", "type": "function", "z": "d67b549d72343dd7", "name": "function 5", "func": "const fileid = msg.fileuuid;\nmsg.payload = {\n    application:\"hiring\",\n    \"fileid\": fileid,\n    \"status\": \"File sent to AI\",\n    \"timestamp\": new Date().toISOString()\n};\n\n// Update the status in the DynamoDB\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1170, "y": 540, "wires": [["c30b8ad3502702af"]]}, {"id": "c30b8ad3502702af", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "", "region": "us-east-1", "operation": "create", "tableName": "ResumeStatus", "schemaFilePath": "", "x": 1350, "y": 540, "wires": [[]]}, {"id": "c4dfb6b98c5c04ee", "type": "function", "z": "d67b549d72343dd7", "name": "function 6", "func": "const fileid = msg.fileuuid;\n\nlet processedJson = msg.payload;\n\nif (typeof processedJson === 'string') {\n    try {\n        processedJson = JSON.parse(processedJson);\n    } catch (e) {\n        node.error(\"Failed to parse processedJson\", e);\n    }\n}\n\nmsg.payload = {\n    \"application\": \"hiring\",\n    \"fileid\": fileid,                     \n    \"originalName\": msg.originalname,         \n    \"processedJson\": processedJson,      \n};\nmsg.processedJson = processedJson;\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 1170, "y": 600, "wires": [["336abc04475808b0", "e51cecd096ab9a2f"]]}, {"id": "336abc04475808b0", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "ProcessedResumeText", "region": "us-east-1", "operation": "create", "tableName": "ExtractedResume", "schemaFilePath": "", "x": 1380, "y": 600, "wires": [[]]}, {"id": "0b4d310b8f6c33b6", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 4", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1170, "y": 660, "wires": []}, {"id": "1961cbd2df21a14c", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 3", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 720, "y": 360, "wires": []}, {"id": "e51cecd096ab9a2f", "type": "function", "z": "d67b549d72343dd7", "name": "Prepare OpenAI Request", "func": "if (!msg.payload.processedJson) {\n    node.error(\"Both resumeData and jobDescription must be provided.\");\n    return;\n}\n\nconst resumeDataString = JSON.stringify(msg.payload.processedJson, null, 2);\nconst jobDescriptionString = JSON.stringify(msg.JobDesc, null, 2);\n\nconst prompt = `Compare the following resume data with the job description and return the result in JSON format, including the candidate's name, match percentage, and a brief explanation.\ndata: ${resumeDataString}\njobData: ${jobDescriptionString}\n\nThe JSON response should look like this:\n{\n    \"candidate_name\": \"string\",\n    \"similarity_percentage\": number,\n    \"explanation\": \"string\"\n}`;\n\nmsg.payload = prompt;\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1310, "y": 720, "wires": [["4542e69179e961c2", "d3a4c84a835ad7c4"]]}, {"id": "d3a4c84a835ad7c4", "type": "lcp-openai", "z": "d67b549d72343dd7", "name": "", "model": "gpt-4o-mini", "maxTokens": "2000", "validateJson": true, "x": 1540, "y": 720, "wires": [["e636b7706928b6f6", "ca37e58097fc17b7", "6068a68a78cbd002"]]}, {"id": "283f3beb426224f2", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 6", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1860, "y": 700, "wires": []}, {"id": "4542e69179e961c2", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 7", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1360, "y": 840, "wires": []}, {"id": "e636b7706928b6f6", "type": "function", "z": "d67b549d72343dd7", "name": "function 8", "func": "const uuidv4 = uuid.v4;\nconst candidateid = uuidv4();\nconst name = msg.processedJson.name;\nconst last_name = msg.processedJson.last_name;\nconst middle_name = msg.processedJson.middle_name;\nconst email = msg.processedJson.email;\nconst status = msg.processedJson.status;\nconst reference_mode = msg.processedJson.reference_mode;\nconst reference_person = msg.processedJson.reference_person;\nconst phone_number = msg.processedJson.phone_number;\nconst years_of_experience = msg.processedJson.years_of_experience;\nconst experience_summary = msg.processedJson.experience_summary;\nconst achievements = msg.processedJson.achievements; // Assuming this is an array\nconst certifications = msg.processedJson.certifications; // Assuming this is an array\nconst education = msg.processedJson.education; // Assuming this is an array of objects\nconst employment_history = msg.processedJson.employment_history; // Assuming this is an array of objects\nconst key_skills = msg.processedJson.key_skills; // Assuming this is an array\nconst resume_explanation = JSON.parse(msg.payload).explanation;\nconst resume_percentage = JSON.parse(msg.payload).similarity_percentage;\nconst current_location = msg.processedJson.current_location;\nconst flexible_with_relocation = msg.processedJson.flexible_with_relocation;\nconst current_notice_period = msg.processedJson.current_notice_period;\nconst current_ctc = msg.processedJson.current_ctc;\nconst expected_ctc = msg.processedJson.expected_ctc;\nconst reason_for_change = msg.processedJson.reason_for_change;\nconst fileid= msg.fileuuid;\nconst job_id = msg.Job_Id;\nconst created_by = msg.context_user;\nconst created_on = new Date();\n\nif (!created_by || created_by === \"\") {\n  msg.payload = \"context user missing\";\n  msg.statusCode = 422;\n  return [null, msg];\n}\n\nmsg.payload = {\n  \"application\": \"candidate\",\n  \"candidateid\": candidateid,\n  \"name\": name,\n  \"last_name\": last_name,\n  \"middle_name\": middle_name,\n  \"email\": email,\n  \"status\":status,\n  \"reference_mode\": reference_mode,\n  \"reference_person\": reference_person,\n  \"phone_number\": phone_number,\n  \"years_of_experience\": years_of_experience,\n  \"experience_summary\": experience_summary,\n\n  // Store achievements with indexed values\n  \"achievements\": achievements.map((achievement) => (\n    achievement\n  )),\n\n  // Store certifications with indexed values\n  \"certifications\": certifications.map((certification) => (\n  certification\n  )),\n\n  // Store education as an array of objects with indexed values\n  \"education\": education.map((edu) => (\n    {\n      \"degree\": edu.degree,\n      \"start_date\": edu.start_date,\n      \"end_date\": edu.end_date,\n      \"field_of_study\": edu.field_of_study,\n      \"institution\": edu.institution\n    }\n  )),\n\n  // Store employment_history as an array of objects with indexed values\n  \"employment_history\": employment_history.map((employment) => (\n    {\n      \"role\": employment.role,\n      \"organization\": employment.organization,\n      \"start_date\": employment.start_date,\n      \"end_date\": employment.end_date\n    }\n  )),\n\n  // Store key_skills with indexed values\n  \"key_skills\": key_skills.map((skill) => (\n    skill\n  )),\n\n  \"Resume_Explanation\": resume_explanation,\n  \"Resume_Percentage\": resume_percentage,\n  \"Job_id\": job_id,\n  \"created_by\": created_by,\n  \"created_on\": created_on.toISOString(),\n\n  // Additional fields\n  \"current_location\": current_location,\n  \"flexible_with_relocation\": flexible_with_relocation,\n  \"current_notice_period\": current_notice_period,\n  \"current_ctc\": current_ctc,\n  \"expected_ctc\": expected_ctc,\n  \"reason_for_change\": reason_for_change,\n  \"fileid\":fileid\n};\n\nreturn [msg, null];\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 1610, "y": 800, "wires": [["57985e92e89da9e8", "5c21fcb85fdace3c"]]}, {"id": "57985e92e89da9e8", "type": "dynamodb", "z": "d67b549d72343dd7", "name": " Dynamo DB", "region": "us-east-1", "operation": "create", "tableName": "LCPCandidate", "schemaFilePath": "dynamodb.schema.json", "x": 1770, "y": 800, "wires": [["30c44cd0d079732c"]]}, {"id": "30c44cd0d079732c", "type": "function", "z": "d67b549d72343dd7", "name": "function 9", "func": "if (msg.payload && msg.payload.Item) {\n    msg.payload = msg.payload.Item;  \n} else {\n    msg.payload = { error: \"No data found\" }; \n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1950, "y": 800, "wires": [["ddcc27941b741440"]]}, {"id": "ddcc27941b741440", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 8", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1940, "y": 860, "wires": []}, {"id": "ca37e58097fc17b7", "type": "function", "z": "d67b549d72343dd7", "name": "function 13", "func": "msg.payload = msg.payload\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1690, "y": 680, "wires": [["283f3beb426224f2"]]}, {"id": "5c21fcb85fdace3c", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 1", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1650, "y": 900, "wires": []}, {"id": "6068a68a78cbd002", "type": "function", "z": "d67b549d72343dd7", "name": "function 1", "func": "msg.payload = msg.payload\n\n\nreturn msg.candidate_name;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1630, "y": 500, "wires": [["b0d413ef351b1354"]]}, {"id": "b0d413ef351b1354", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 2", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1850, "y": 520, "wires": []}]
[{"id": "d67b549d72343dd7", "type": "tab", "label": "Resume", "disabled": false, "info": "", "env": []}, {"id": "85d3a8b50092132e", "type": "tab", "label": " JDFlows", "disabled": false, "info": "", "env": []}, {"id": "40d36f1d8996c424", "type": "function", "z": "d67b549d72343dd7", "name": "function 10", "func": "msg.Job_Id = msg.req.body.Job_Id\nmsg.payload = msg.req.files\nmsg.context_user =  msg.req.headers['context_user']\nmsg.JobDesc = msg.req.body.JobDesc\nreturn msg", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 330, "y": 420, "wires": [["671a946a7c57fd01", "6d987b6c13720b56"]]}, {"id": "671a946a7c57fd01", "type": "split", "z": "d67b549d72343dd7", "name": "", "splt": "\\n", "spltType": "str", "arraySplt": 1, "arraySpltType": "len", "stream": false, "addname": "", "property": "payload", "x": 490, "y": 420, "wires": [["20ccf6f1553aedc9"]]}, {"id": "23ed14edd5c4e93b", "type": "http in", "z": "d67b549d72343dd7", "name": "", "url": "/multi-resume-upload", "method": "post", "upload": true, "swaggerDoc": "", "x": 190, "y": 380, "wires": [["40d36f1d8996c424"]]}, {"id": "20ccf6f1553aedc9", "type": "function", "z": "d67b549d72343dd7", "name": "function 11", "func": "    const hasFile = msg.payload\n\n    if (hasFile) {\n    const uuidv4 = uuid.v4;\n        msg.originalname = msg.payload.originalname;\n    if (!msg.originalname.endsWith(\".pdf\")) {\n        msg.payload.error = \"file type not supported\";\n        msg.statusCode = 400;\n        return msg;\n    }\n    msg.payload = msg.payload.buffer;\n    msg.filename = uuidv4();\n\n    msg.user = 'contextUser';\n    msg.attachment = msg.payload;\n    msg.fileuuid = msg.filename;\n    return msg\n    } else {\n    msg.payload.error = \"file not found\";\n    msg.statusCode = 400;\n    return msg\n    }\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 630, "y": 360, "wires": [["373c917d590c5912"]]}, {"id": "373c917d590c5912", "type": "s3-upload", "z": "d67b549d72343dd7", "name": "S3 Upload", "bucket": "RESUME_BUCKET", "operation": "upload", "x": 830, "y": 360, "wires": [["a0d788a2a579d129", "9ac88d31a0f5082c"], []]}, {"id": "f80d4233b356d7fc", "type": "function", "z": "d67b549d72343dd7", "name": "function 12", "func": "\nmsg.payload = {\n    \"fileuuid\": msg.fileuuid,\n    \"originalname\": msg.originalname\n    };\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 710, "y": 420, "wires": [["ba7e4349e9d6840c"]]}, {"id": "ba7e4349e9d6840c", "type": "join", "z": "d67b549d72343dd7", "name": "", "mode": "auto", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": false, "accumulate": true, "timeout": "", "count": "", "reduceRight": false, "reduceExp": "", "reduceInit": "", "reduceInitType": "", "reduceFixup": "", "x": 870, "y": 420, "wires": [["b6320e25a0fed07d"]]}, {"id": "b6320e25a0fed07d", "type": "http response", "z": "d67b549d72343dd7", "name": "", "statusCode": "", "headers": {}, "x": 1030, "y": 420, "wires": []}, {"id": "a0d788a2a579d129", "type": "function", "z": "d67b549d72343dd7", "name": "function 3", "func": "msg.payload = {\n    \"application\": \"hiring\",\n                \"fileuuid\":msg.filename,\n                \"originalname\":msg.originalname,\n                \"bucket\": \"lcp-resume-west\",\n                \"status\" : \"File has been uploaded successfully\"\n                }\nconst uuidv4 = uuid.v4;\nconst id = uuidv4()\nmsg.newCandidateID = id;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 1000, "y": 360, "wires": [["86d54338ff72d2a3", "12ecff6e52068de4"]]}, {"id": "86d54338ff72d2a3", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "", "region": "us-east-1", "operation": "create", "tableName": "FileNameMap", "schemaFilePath": "", "x": 1210, "y": 380, "wires": [["ab6a42dd42c9241d"]]}, {"id": "ab6a42dd42c9241d", "type": "change", "z": "d67b549d72343dd7", "name": "SetPayload", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "attachment", "tot": "msg"}, {"t": "set", "p": "attachment", "pt": "msg", "to": "null", "tot": "jsonata"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 610, "y": 560, "wires": [["877c7657c8a43aae", "96275cb842586150"]]}, {"id": "877c7657c8a43aae", "type": "pdf-extract", "z": "d67b549d72343dd7", "name": "", "x": 810, "y": 560, "wires": [["3f84fa1128d2680b", "02c222ea22c947af"]]}, {"id": "02c222ea22c947af", "type": "function", "z": "d67b549d72343dd7", "name": "function 4", "func": "\nmsg.payload = {\n    application:\"hiring\",\n    \"fileid\": msg.filename,\n    \"status\": \"PDF Extracted\",\n    \"timestamp\": new Date().toISOString()\n};\n\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1010, "y": 520, "wires": [["47d41e67c4eed511"]]}, {"id": "47d41e67c4eed511", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "", "region": "us-east-1", "operation": "create", "tableName": "ResumeStatus", "schemaFilePath": "", "x": 1210, "y": 520, "wires": [[]]}, {"id": "3f84fa1128d2680b", "type": "text-preprocessor", "z": "d67b549d72343dd7", "name": "", "x": 1030, "y": 560, "wires": [["8ff874b109089e96"]]}, {"id": "8ff874b109089e96", "type": "function", "z": "d67b549d72343dd7", "name": "Chunks", "func": "msg.payload = msg.payload.chunks\n// if (Array.isArray(msg.payload)) {\n//     msg.payload = msg.payload.chunks;\n// } else {\n//     node.error('Payload is not an array of chunks', msg);\n// }\nreturn msg;\n\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1200, "y": 560, "wires": [["b2a9969dc5e85a36"]]}, {"id": "b2a9969dc5e85a36", "type": "split", "z": "d67b549d72343dd7", "name": "", "splt": "\\n", "spltType": "str", "arraySplt": 1, "arraySpltType": "len", "stream": false, "addname": "", "property": "payload", "x": 1370, "y": 560, "wires": [["2dc9fa782abaaf7f"]]}, {"id": "2dc9fa782abaaf7f", "type": "function", "z": "d67b549d72343dd7", "name": "Prompt for Resume", "func": "msg.topic = \"GPT-4o-mini\"\nmsg.payload = \"You are an AI assistant specialized in parsing resumes. Extract the requested information and format it as a JSON object and return the JSON object only and nothing else.\" +\n    \"Text of CV from a candidate can be found below\" +\n    \"You need to analyze and extract key information like First Name, Middle Name, Last Name, skills, experience summary and all the other details present in the resume text and return it in JSON object format. return valid JSON object only\" +\n    \"Candidate CV \\n\" + msg.payload\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 830, "y": 620, "wires": [["c3343fd88ed9c6cf"]]}, {"id": "c3343fd88ed9c6cf", "type": "lcp-openai", "z": "d67b549d72343dd7", "name": "", "model": "gpt-4o-mini", "maxTokens": "2000", "validateJson": false, "x": 1000, "y": 620, "wires": [["07a6ab52a5a2dca6"]]}, {"id": "07a6ab52a5a2dca6", "type": "join", "z": "d67b549d72343dd7", "name": "", "mode": "auto", "build": "object", "property": "payload", "propertyType": "msg", "key": "topic", "joiner": "\\n", "joinerType": "str", "useparts": false, "accumulate": "false", "timeout": "", "count": "", "reduceRight": false, "x": 1190, "y": 620, "wires": [["4bb45730d736e8aa"]]}, {"id": "4bb45730d736e8aa", "type": "function", "z": "d67b549d72343dd7", "name": "Prompt for JSON", "func": "msg.topic = \"GPT-4o-mini\";\nmsg.payload = `The following JSON template represents the structure of a resume. Please fill in the missing fields based on the provided text or correct any syntax issues if present. Return only the valid JSON without any additional text also Please send only JSON object and nothing else.\n\nTemplate:\n{\n    \"name\": \"Full Name\",\n    \"email\": \"Email Address\",\n    \"phone_number\": \"Phone Number\",\n    \"key_skills\": [\"Skill 1\", \"Skill 2\", ...],\n    \"years_of_experience\": Number,\n    \"experience_summary\": \"Brief summary\",\n    \"employment_history\": [\n        {\n            \"organization\": \"Company Name\",\n            \"role\": \"Job Title\",\n            \"start_date\": \"YYYY-MM\",\n            \"end_date\": \"YYYY-MM or 'Present'\"\n        },\n        ...\n    ],\n    \"education\": [\n        {\n            \"institution\": \"Institution Name\",\n            \"degree\": \"Degree\",\n            \"field_of_study\": \"Field of Study\",\n            \"start_date\": \"YYYY-MM\",\n            \"end_date\": \"YYYY-MM or 'Present'\"\n        },\n        ...\n    ],\n    \"achievements\": [\"Achievement 1\", \"Achievement 2\", ...],\n    \"certifications\": [\"Certification 1\", \"Certification 2\", ...]\n}\n\nText to process:\n${msg.payload}`;\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 830, "y": 680, "wires": [["4b05299c5abec3b0"]]}, {"id": "4b05299c5abec3b0", "type": "lcp-openai", "z": "d67b549d72343dd7", "name": "", "model": "gpt-4o-mini", "maxTokens": "2000", "x": 1000, "y": 680, "wires": [["433d024b11f5f13a", "e48acc487aa79e97", "ff3ea9c1f09fd341"]]}, {"id": "433d024b11f5f13a", "type": "function", "z": "d67b549d72343dd7", "name": "function 5", "func": "const fileid = msg.fileuuid;\nmsg.payload = {\n    application:\"hiring\",\n    \"fileid\": fileid,\n    \"status\": \"File sent to AI\",\n    \"timestamp\": new Date().toISOString()\n};\n\n// Update the status in the DynamoDB\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1210, "y": 680, "wires": [["fa805ae6eb3f5982"]]}, {"id": "fa805ae6eb3f5982", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "", "region": "us-east-1", "operation": "create", "tableName": "ResumeStatus", "schemaFilePath": "", "x": 1390, "y": 680, "wires": [[]]}, {"id": "e48acc487aa79e97", "type": "function", "z": "d67b549d72343dd7", "name": "function 6", "func": "const fileid = msg.fileuuid;\n\nlet processedJson = msg.payload;\n\nif (typeof processedJson === 'string') {\n    try {\n        processedJson = JSON.parse(processedJson);\n    } catch (e) {\n        node.error(\"Failed to parse processedJson\", e);\n    }\n}\n\nmsg.payload = {\n    \"application\": \"hiring\",\n    \"fileid\": fileid,                     \n    \"originalName\": msg.originalname,         \n    \"processedJson\": processedJson,      \n};\nmsg.processedJson = processedJson;\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 1210, "y": 740, "wires": [["64083ce932bc2c17", "31fd0c389202c97c"]]}, {"id": "64083ce932bc2c17", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "ProcessedResumeText", "region": "us-east-1", "operation": "create", "tableName": "ExtractedResume", "schemaFilePath": "", "x": 1420, "y": 740, "wires": [[]]}, {"id": "ff3ea9c1f09fd341", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 4", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1210, "y": 800, "wires": []}, {"id": "96275cb842586150", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 3", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 800, "y": 520, "wires": []}, {"id": "31fd0c389202c97c", "type": "function", "z": "d67b549d72343dd7", "name": "Prepare OpenAI Request", "func": "if (!msg.payload.processedJson) {\n    node.error(\"Both resumeData and jobDescription must be provided.\");\n    return;\n}\n\nconst resumeDataString = JSON.stringify(msg.payload.processedJson, null, 2);\nconst jobDescriptionString = JSON.stringify(msg.JobDesc, null, 2);\n\nconst prompt = `Compare the following resume data with the job description and return the result in JSON format, including the candidate's name, match percentage, and a brief explanation.\ndata: ${resumeDataString}\njobData: ${jobDescriptionString}\n\nThe JSON response should look like this:\n{\n    \"candidate_name\": \"string\",\n    \"similarity_percentage\": number,\n    \"explanation\": \"string\"\n}`;\n\nmsg.payload = prompt;\nreturn msg;", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1350, "y": 860, "wires": [["3bc4536063026b50", "8ca0e1024ae88996"]]}, {"id": "8ca0e1024ae88996", "type": "lcp-openai", "z": "d67b549d72343dd7", "name": "", "model": "gpt-4o-mini", "maxTokens": "2000", "validateJson": true, "x": 1580, "y": 860, "wires": [["10380bb447ca09ed", "6f9ba18f9fc08cd6"]]}, {"id": "4d808c0ddc9bbd33", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 6", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1900, "y": 840, "wires": []}, {"id": "3bc4536063026b50", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 7", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1400, "y": 980, "wires": []}, {"id": "10380bb447ca09ed", "type": "function", "z": "d67b549d72343dd7", "name": "function 8", "func": "// const uuidv4 = uuid.v4;\nconst candidateid = msg.newCandidateID;\nconst name = msg.processedJson.name;\nconst last_name = msg.processedJson.last_name;\nconst middle_name = msg.processedJson.middle_name;\nconst email = msg.processedJson.email;\nconst status = msg.processedJson.status || 'initial';\nconst reference_mode = msg.processedJson.reference_mode;\nconst reference_person = msg.processedJson.reference_person;\nconst phone_number = msg.processedJson.phone_number;\nconst years_of_experience = msg.processedJson.years_of_experience;\nconst experience_summary = msg.processedJson.experience_summary;\nconst achievements = msg.processedJson.achievements; // Assuming this is an array\nconst certifications = msg.processedJson.certifications; // Assuming this is an array\nconst education = msg.processedJson.education; // Assuming this is an array of objects\nconst employment_history = msg.processedJson.employment_history; // Assuming this is an array of objects\nconst key_skills = msg.processedJson.key_skills; // Assuming this is an array\nconst resume_explanation = JSON.parse(msg.payload).explanation;\nconst resume_percentage = JSON.parse(msg.payload).similarity_percentage;\nconst current_location = msg.processedJson.current_location;\nconst flexible_with_relocation = msg.processedJson.flexible_with_relocation;\nconst current_notice_period = msg.processedJson.current_notice_period;\nconst current_ctc = msg.processedJson.current_ctc;\nconst expected_ctc = msg.processedJson.expected_ctc;\nconst reason_for_change = msg.processedJson.reason_for_change;\nconst fileid= msg.fileuuid;\nconst job_id = msg.Job_Id;\nconst created_by = msg.context_user;\nconst created_on = new Date();\n\nif (!created_by || created_by === \"\") {\n  msg.payload = \"context user missing\";\n  msg.statusCode = 422;\n  return [null, msg];\n}\n\nmsg.payload = {\n  \"application\": \"candidate\",\n  \"candidateid\": candidateid,\n  \"name\": name,\n  \"last_name\": last_name,\n  \"middle_name\": middle_name,\n  \"email\": email,\n  \"status\":status,\n  \"reference_mode\": reference_mode,\n  \"reference_person\": reference_person,\n  \"phone_number\": phone_number,\n  \"years_of_experience\": years_of_experience,\n  \"experience_summary\": experience_summary,\n\n  // Store achievements with indexed values\n  \"achievements\": achievements.map((achievement) => (\n    achievement\n  )),\n\n  // Store certifications with indexed values\n  \"certifications\": certifications.map((certification) => (\n  certification\n  )),\n\n  // Store education as an array of objects with indexed values\n  \"education\": education.map((edu) => (\n    {\n      \"degree\": edu.degree,\n      \"start_date\": edu.start_date,\n      \"end_date\": edu.end_date,\n      \"field_of_study\": edu.field_of_study,\n      \"institution\": edu.institution\n    }\n  )),\n\n  // Store employment_history as an array of objects with indexed values\n  \"employment_history\": employment_history.map((employment) => (\n    {\n      \"role\": employment.role,\n      \"organization\": employment.organization,\n      \"start_date\": employment.start_date,\n      \"end_date\": employment.end_date\n    }\n  )),\n\n  // Store key_skills with indexed values\n  \"key_skills\": key_skills.map((skill) => (\n    skill\n  )),\n\n  \"Resume_Explanation\": resume_explanation,\n  \"Resume_Percentage\": resume_percentage,\n  \"Job_id\": job_id,\n  \"created_by\": created_by,\n  \"created_on\": created_on.toISOString(),\n\n  // Additional fields\n  \"current_location\": current_location,\n  \"flexible_with_relocation\": flexible_with_relocation,\n  \"current_notice_period\": current_notice_period,\n  \"current_ctc\": current_ctc,\n  \"expected_ctc\": expected_ctc,\n  \"reason_for_change\": reason_for_change,\n  \"fileid\":fileid\n};\n\nreturn [msg, null];\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 1650, "y": 940, "wires": [["c80a44079316772b", "4f665ad32e91a663"]]}, {"id": "c80a44079316772b", "type": "dynamodb", "z": "d67b549d72343dd7", "name": " Dynamo DB", "region": "us-east-1", "operation": "update", "tableName": "LCPCandidate", "schemaFilePath": "", "x": 1810, "y": 940, "wires": [["eb2fb57a21666a73"]]}, {"id": "eb2fb57a21666a73", "type": "function", "z": "d67b549d72343dd7", "name": "function 9", "func": "if (msg.payload && msg.payload.Item) {\n    msg.payload = msg.payload.Item;  \n} else {\n    msg.payload = { error: \"No data found\" }; \n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1990, "y": 940, "wires": [["12ab4582b42a3a0a"]]}, {"id": "12ab4582b42a3a0a", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 8", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1980, "y": 1000, "wires": []}, {"id": "6f9ba18f9fc08cd6", "type": "function", "z": "d67b549d72343dd7", "name": "function 13", "func": "msg.payload = msg.payload\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1730, "y": 820, "wires": [["4d808c0ddc9bbd33"]]}, {"id": "9ac88d31a0f5082c", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 14", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 900, "y": 300, "wires": []}, {"id": "6d987b6c13720b56", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 15", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 420, "y": 340, "wires": []}, {"id": "542dab7daeaa4b37", "type": "dynamodb", "z": "d67b549d72343dd7", "name": "Init Candidate", "region": "us-east-1", "operation": "create", "tableName": "LCPCandidate", "schemaFilePath": "", "x": 1340, "y": 300, "wires": [["f80d4233b356d7fc"]]}, {"id": "12ecff6e52068de4", "type": "function", "z": "d67b549d72343dd7", "name": "function 17", "func": "const job_id = msg.Job_Id;\nmsg.payload = {\n    \"application\": \"candidate\",\n    \"candidateid\": msg.newCandidateID,\n    \"Job_id\":job_id,\n    \"status\":\"resume extracting\"\n}\n\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 1130, "y": 300, "wires": [["542dab7daeaa4b37"]]}, {"id": "4f665ad32e91a663", "type": "debug", "z": "d67b549d72343dd7", "name": "debug 16", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1680, "y": 1020, "wires": []}, {"id": "69ec0aec55401e04", "type": "function", "z": "85d3a8b50092132e", "name": "function 42", "func": "const uuidv4 = uuid.v4;\nconst jobdescid = uuidv4();\nconst jobtitle = msg.req.body.jobtitle;\nconst location = msg.req.body.location;\nconst requirement = msg.req.body.requirement;\nconst status = msg.req.body.status;\nconst skills = msg.req.body.skills;\nconst responsibilities = msg.req.body.responsibilities;\nconst created_by = msg.req.body.created_by;\nconst publishToExternalChannels = msg.req.body.publishToExternalChannels;\nconst organization = msg.req.body.organization\n\nconst created_on = new Date();\n\nif (!created_by || created_by == \"\" ) {\n    msg.payload = \"context user missing\";\n    msg.statusCode = 422;\n    return [null,msg]\n}\nmsg.payload = {\n  \"application\": \"jobdes\",\n  \"jobdescid\": jobdescid,\n    \"jobtitle\": jobtitle,\n    \"location\": location,\n    \"requirement\": requirement,\n  \"skills\": skills,\n  \"status\": status,\n  \"created_by\": created_by,\n  \"responsibilities\": responsibilities,\n  \"publishToExternalChannels\": publishToExternalChannels,\n  \"organization\": organization\n}\nreturn [msg,null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 270, "y": 260, "wires": [["36162ee62e58b914", "0b29005d0b69690f"], []]}, {"id": "bf1bb9b7de17b5ee", "type": "http in", "z": "85d3a8b50092132e", "name": "<PERSON><PERSON> <PERSON><PERSON>", "url": "/jobdes", "method": "post", "upload": false, "swaggerDoc": "", "x": 80, "y": 260, "wires": [["69ec0aec55401e04"]]}, {"id": "887bd1bfc6719885", "type": "http response", "z": "85d3a8b50092132e", "name": "", "statusCode": "", "headers": {}, "x": 790, "y": 260, "wires": []}, {"id": "8cf32a7e94eea39a", "type": "function", "z": "85d3a8b50092132e", "name": "function 50", "func": "const uuidv4 = uuid.v4;\nconst jobdescid = msg.req.body.jobdescid;;\nconst jobtitle = msg.req.body.jobtitle;\nconst location = msg.req.body.location;\nconst requirement = msg.req.body.requirement;\nconst status = msg.req.body.status;\nconst skills = msg.req.body.skills;\nconst responsibilities = msg.req.body.responsibilities;\nconst created_by = msg.req.body.created_by;\nconst created_on = new Date();\nconsole.log(\"created_by\", created_by)\nconst publishToExternalChannels = msg.req.body.publishToExternalChannels;\n\nif (!created_by || created_by == \"\") {\n  msg.payload = \"context user missing\";\n  msg.statusCode = 422;\n  return [null, msg]\n}\nmsg.payload = {\n  \"application\": \"jobdes\",\n  \"jobdescid\": jobdescid,\n  \"jobtitle\": jobtitle,\n  \"location\": location,\n  \"requirement\": requirement,\n  \"skills\": skills,\n  \"status\": status,\n  \"responsibilities\": responsibilities,\n  \"publishToExternalChannels\": publishToExternalChannels\n}\nconsole.log(msg.payload, \"msg.payload\")\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [{"var": "uuid", "module": "uuid"}], "x": 250, "y": 340, "wires": [["ba54ed11e97be690"], []]}, {"id": "9b7fa1804284f5e6", "type": "http in", "z": "85d3a8b50092132e", "name": "update Jobdes", "url": "/jobdes", "method": "put", "upload": false, "swaggerDoc": "", "x": 80, "y": 340, "wires": [["8cf32a7e94eea39a"]]}, {"id": "928b2c5a5df33d18", "type": "http response", "z": "85d3a8b50092132e", "name": "", "statusCode": "", "headers": {}, "x": 790, "y": 340, "wires": []}, {"id": "b8414941b310bdce", "type": "function", "z": "85d3a8b50092132e", "name": "function 79", "func": "if (msg.payload && msg.payload.Item) {\n    msg.payload = msg.payload.Item;  \n} else {\n    msg.payload = { error: \"No data found\" }; \n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 630, "y": 240, "wires": [["887bd1bfc6719885", "94244e36e7b89414"]]}, {"id": "d399f864181db139", "type": "function", "z": "85d3a8b50092132e", "name": "function 83", "func": "if (msg.payload && msg.payload.Item) {\n    msg.payload = msg.payload.Item;  \n} else {\n    msg.payload = { error: \"No data found\" }; \n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 630, "y": 340, "wires": [["928b2c5a5df33d18"]]}, {"id": "d473d4a06e0ff066", "type": "http in", "z": "85d3a8b50092132e", "name": "get jobdesc", "url": "/fetch", "method": "get", "upload": false, "swaggerDoc": "", "x": 70, "y": 440, "wires": [["2d2a11597f313431"]]}, {"id": "2d2a11597f313431", "type": "function", "z": "85d3a8b50092132e", "name": "Initialize Query", "func": "const { application, startKey } = msg.payload;\n\nmsg.payload = {\n    application: 'jobdes', \n    KeyConditionExpression: '#application = :application', \n    ExpressionAttributeNames: {\n        '#application': 'application' \n    },\n    ExpressionAttributeValues: {\n        ':application': application\n    },\n    ExclusiveStartKey: startKey\n};\n\nreturn msg;\n", "outputs": 1, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 260, "y": 440, "wires": [["3dfe9cdd866c3afd"]]}, {"id": "803b973a4aa0f5cd", "type": "http response", "z": "85d3a8b50092132e", "name": "HTTP ", "statusCode": "", "headers": {}, "x": 790, "y": 440, "wires": []}, {"id": "cd28bf7ca903ce8c", "type": "http in", "z": "85d3a8b50092132e", "name": "", "url": "/fetch-item", "method": "get", "upload": false, "swaggerDoc": "", "x": 80, "y": 540, "wires": [["02d10bb56278cb27"]]}, {"id": "0e5ee3c214f7fda2", "type": "http response", "z": "85d3a8b50092132e", "name": "", "statusCode": "200", "headers": {}, "x": 800, "y": 540, "wires": []}, {"id": "02d10bb56278cb27", "type": "function", "z": "85d3a8b50092132e", "name": "function 89", "func": "console.log(msg.req.query)\nmsg.payload = {\n    Key: {\n        application: 'jobdes',\n        jobdescid:msg.req.query.jobdescid,  // Use your partition key's attribute name here \n    }\n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 270, "y": 540, "wires": [["182d993cbd9adbca"]]}, {"id": "72ead2699293467e", "type": "function", "z": "85d3a8b50092132e", "name": "function 90", "func": "if (msg.payload && msg.payload.Item) {\n    msg.payload = msg.payload.Item;  \n} else {\n    msg.payload = { error: \"No data found\" }; \n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 630, "y": 540, "wires": [["0e5ee3c214f7fda2"]]}, {"id": "8931e884544f9414", "type": "function", "z": "85d3a8b50092132e", "name": "function 84", "func": "if (msg.payload && msg.payload.Items) {\n    msg.payload = msg.payload.Items?.filter(item => item?.publishToExternalChannels === true)?.map(it=> \n    {\n       return {\n           \"location\": it?.location,\n           \"responsibilities\": it?.responsibilities,\n           \"jobdescid\": it?.jobdescid,\n           \"created_by\": it?.created_by,\n           \"requirement\": it?.requirement,\n           \"application\": it?.application,\n           \"skills\": it?.skills,\n           \"jobtitle\": it?.jobtitle\n       }\n    });  \n}\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 630, "y": 440, "wires": [["803b973a4aa0f5cd"]]}, {"id": "3a94be58d146e794", "type": "http in", "z": "85d3a8b50092132e", "name": "", "url": "/filterJobDescription", "method": "get", "upload": false, "swaggerDoc": "", "x": 125, "y": 618.5714111328125, "wires": [["4cd4f17f79275be7"]]}, {"id": "4cd4f17f79275be7", "type": "function", "z": "85d3a8b50092132e", "name": "function 134", "func": "msg.payload = {\n    application: \"jobdes\",\n    status: msg.req.query.status,\n    organization: msg.req.query.organization\n}\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 330, "y": 620, "wires": [["60c5af0d9b44b077"]]}, {"id": "cc8832351287dee9", "type": "function", "z": "85d3a8b50092132e", "name": "function 135", "func": "if (msg.payload && msg.payload.Items) {\n    msg.payload = msg.payload.Items;  \n} \nreturn msg", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 665, "y": 618.5714111328125, "wires": [["4ba8c263a9e49789"]]}, {"id": "4ba8c263a9e49789", "type": "http response", "z": "85d3a8b50092132e", "name": "", "statusCode": "200", "headers": {}, "x": 835, "y": 618.5714111328125, "wires": []}, {"id": "ec2caeecabab73ca", "type": "http in", "z": "85d3a8b50092132e", "name": "", "url": "/jd-item-delete", "method": "delete", "upload": false, "swaggerDoc": "", "x": 105, "y": 838.5714111328125, "wires": [["834b62cefe642706"]]}, {"id": "8df494256c2903f6", "type": "http response", "z": "85d3a8b50092132e", "name": "", "statusCode": "200", "headers": {}, "x": 800, "y": 840, "wires": []}, {"id": "834b62cefe642706", "type": "function", "z": "85d3a8b50092132e", "name": "function 136", "func": "msg.payload = {\n         application: 'jobdes',\n        jobdescid:msg.req.query.jobdescid ,\n};\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 310, "y": 840, "wires": [["085677202b922e46"]]}, {"id": "ef7d985917ee29f3", "type": "function", "z": "85d3a8b50092132e", "name": "function 137", "func": "msg.payload = \"deleted\"\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 650, "y": 840, "wires": [["8df494256c2903f6"]]}, {"id": "36162ee62e58b914", "type": "dynamodb", "z": "85d3a8b50092132e", "name": " Dynamo DB", "region": "us-east-1", "operation": "create", "tableName": "LCPJobDes", "schemaFilePath": "dynamodb.schema.json", "x": 450, "y": 260, "wires": [["b8414941b310bdce", "551183126f6a76d4"]]}, {"id": "ba54ed11e97be690", "type": "dynamodb", "z": "85d3a8b50092132e", "name": " Dynamo DB", "region": "us-east-1", "operation": "update", "tableName": "LCPJobDes", "schemaFilePath": "dynamodb.schema.json", "x": 450, "y": 340, "wires": [["d399f864181db139"]]}, {"id": "3dfe9cdd866c3afd", "type": "dynamodb", "z": "85d3a8b50092132e", "name": "", "region": "us-east-1", "operation": "readAll", "tableName": "LCPJobDes", "schemaFilePath": "", "x": 450, "y": 440, "wires": [["8931e884544f9414"]]}, {"id": "182d993cbd9adbca", "type": "dynamodb", "z": "85d3a8b50092132e", "name": "", "region": "us-east-1", "operation": "read", "tableName": "LCPJobDes", "schemaFilePath": "", "x": 450, "y": 540, "wires": [["72ead2699293467e", "1be45388d86ef030"]]}, {"id": "60c5af0d9b44b077", "type": "dynamodb", "z": "85d3a8b50092132e", "name": "", "region": "us-east-1", "operation": "readAllByQuery", "tableName": "LCPJobDes", "schemaFilePath": "dynamodb.schema.json", "x": 485, "y": 618.5714111328125, "wires": [["cc8832351287dee9", "a39b10e3686c7707"]]}, {"id": "085677202b922e46", "type": "dynamodb", "z": "85d3a8b50092132e", "name": "", "region": "us-east-1", "operation": "delete", "tableName": "LCPJobDes", "schemaFilePath": "dynamodb.schema.json", "x": 470, "y": 840, "wires": [["ef7d985917ee29f3"]]}, {"id": "1be45388d86ef030", "type": "debug", "z": "85d3a8b50092132e", "name": "debug 1", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 580, "y": 480, "wires": []}, {"id": "a39b10e3686c7707", "type": "debug", "z": "85d3a8b50092132e", "name": "debug 2", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 620, "y": 580, "wires": []}, {"id": "551183126f6a76d4", "type": "debug", "z": "85d3a8b50092132e", "name": "debug 5", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 590, "y": 60, "wires": []}, {"id": "94244e36e7b89414", "type": "debug", "z": "85d3a8b50092132e", "name": "debug 9", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 870, "y": 100, "wires": []}, {"id": "0b29005d0b69690f", "type": "debug", "z": "85d3a8b50092132e", "name": "debug 10", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 370, "y": 140, "wires": []}]
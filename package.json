{"name": "airp-nrflows", "version": "1.0.0", "description": "", "main": "settings.js", "scripts": {"dev": "node-red -u . -s settings.js"}, "repository": {"type": "git", "url": "git+https://github.com/rahul-neartekpod/AIRP-NRflows.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/rahul-neartekpod/AIRP-NRflows/issues"}, "homepage": "https://github.com/rahul-neartekpod/AIRP-NRflows#readme", "dependencies": {"dotenv": "^16.5.0", "lcp-nodes": "^1.0.43", "node-red": "^4.0.9", "uuid": "^11.1.0"}}